import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { api } from "../../lib/api";
import type {
  CreateUserRequest,
  UpdateUserRequest,
  User,
} from "../../types/auth";
import { But<PERSON> } from "../ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Switch } from "../ui/switch";

interface UserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user?: User | null;
  onSuccess: () => void;
}

interface FormData {
  username: string;
  real_name: string;
  is_active: boolean;
  is_superuser: boolean;
  password?: string;
}

export function UserDialog({
  open,
  onOpenChange,
  user,
  onSuccess,
}: UserDialogProps) {
  const isEdit = !!user;

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    defaultValues: {
      username: "",
      real_name: "",
      is_active: true,
      is_superuser: false,
      password: "",
    },
  });

  const watchIsActive = watch("is_active");
  const watchIsSuperuser = watch("is_superuser");

  useEffect(() => {
    if (user) {
      reset({
        username: user.username,
        real_name: user.real_name || "",
        is_active: user.is_active,
        is_superuser: user.is_superuser,
        password: "",
      });
    } else {
      reset({
        username: "",
        real_name: "",
        is_active: true,
        is_superuser: false,
        password: "",
      });
    }
  }, [user, reset]);

  const onSubmit = async (data: FormData) => {
    try {
      if (isEdit) {
        const updateData: UpdateUserRequest = {
          id: user!.id,
          username: data.username,
          real_name: data.real_name || undefined,
          is_active: data.is_active,
          is_superuser: data.is_superuser,
        };
        await api.updateUser(updateData);
        toast.success("用户更新成功");
      } else {
        const createData: CreateUserRequest = {
          username: data.username,
          real_name: data.real_name || undefined,
          is_active: data.is_active,
          is_superuser: data.is_superuser,
          password: data.password,
        };
        await api.createUser(createData);
        toast.success("用户创建成功");
      }

      onSuccess();
      onOpenChange(false);
    } catch (error: any) {
      toast.error(error.message || "操作失败");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{isEdit ? "编辑用户" : "创建用户"}</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="username">用户名 *</Label>
            <Input
              id="username"
              {...register("username", {
                required: "用户名不能为空",
                minLength: { value: 3, message: "用户名至少3个字符" },
              })}
              placeholder="输入用户名"
            />
            {errors.username && (
              <p className="text-sm text-destructive">
                {errors.username.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="real_name">真实姓名</Label>
            <Input
              id="real_name"
              {...register("real_name")}
              placeholder="输入真实姓名"
            />
          </div>

          {!isEdit && (
            <div className="space-y-2">
              <Label htmlFor="password">密码 *</Label>
              <Input
                id="password"
                type="password"
                {...register("password", {
                  required: !isEdit ? "密码不能为空" : false,
                  minLength: { value: 6, message: "密码至少6个字符" },
                })}
                placeholder="输入密码"
              />
              {errors.password && (
                <p className="text-sm text-destructive">
                  {errors.password.message}
                </p>
              )}
            </div>
          )}

          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>启用状态</Label>
                <p className="text-sm text-muted-foreground">
                  禁用的用户无法登录系统
                </p>
              </div>
              <Switch
                checked={watchIsActive}
                onCheckedChange={(checked) => setValue("is_active", checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>超级管理员</Label>
                <p className="text-sm text-muted-foreground">
                  拥有所有系统权限
                </p>
              </div>
              <Switch
                checked={watchIsSuperuser}
                onCheckedChange={(checked) => setValue("is_superuser", checked)}
              />
            </div>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "保存中..." : isEdit ? "更新" : "创建"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
