import { Edit, Plus, Search, Shield, Trash2, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import useSWR from "swr";
import { api } from "../../lib/api";
import { useAuthStore } from "../../store/auth-store";
import type { PaginationParams, User } from "../../types/auth";
import { DeleteConfirmDialog } from "../shared/DeleteConfirmDialog";
import {
  TablePagination,
  calculateTotalPages,
} from "../shared/TablePagination";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Input } from "../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Skeleton } from "../ui/skeleton";
import { Switch } from "../ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { RoleAssignmentDialog } from "./RoleAssignmentDialog";
import { UserDialog } from "./UserDialog";

export function UserManagement() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [selectedStatus, setSelectedStatus] = useState<string>("");
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    size: 10,
  });
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [roleAssignmentOpen, setRoleAssignmentOpen] = useState(false);

  const {
    userDialogOpen,
    setUserDialogOpen,
    selectedUser,
    setSelectedUser,
    deleteConfirmOpen,
    setDeleteConfirmOpen,
    deleteTarget,
    setDeleteTarget,
  } = useAuthStore();

  // Fetch users with SWR
  const {
    data: usersData,
    error,
    mutate,
    isLoading,
  } = useSWR(
    ["users", pagination, searchTerm, selectedRole, selectedStatus],
    () =>
      api.getUsers({
        ...pagination,
        search: searchTerm,
        role: selectedRole,
        status: selectedStatus,
      }),
    {
      revalidateOnFocus: false,
      dedupingInterval: 5000,
    }
  );

  // Fetch roles for filtering
  const { data: rolesData } = useSWR("roles", () => api.getRoles());

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const handleRoleFilter = (value: string) => {
    setSelectedRole(value);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const handleStatusFilter = (value: string) => {
    setSelectedStatus(value);
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedRole("");
    setSelectedStatus("");
    setPagination((prev) => ({ ...prev, page: 1 }));
  };

  const handlePageChange = (page: number) => {
    setPagination((prev) => ({ ...prev, page }));
  };

  const handlePageSizeChange = (size: number) => {
    setPagination((prev) => ({ ...prev, size, page: 1 }));
  };

  // Since filtering is now handled by the API, we can use the data directly
  const filteredUsers = usersData?.items || [];

  // Check if any filters are applied
  const hasActiveFilters = searchTerm || selectedRole || selectedStatus;

  const handleCreateUser = () => {
    setSelectedUser(null);
    setUserDialogOpen(true);
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setUserDialogOpen(true);
  };

  const handleDeleteUser = (user: User) => {
    setDeleteTarget({ type: "user", id: user.id });
    setDeleteConfirmOpen(true);
  };

  const handleAssignRoles = (user: User) => {
    setSelectedUserId(user.id);
    setRoleAssignmentOpen(true);
  };

  const handleToggleActive = async (user: User) => {
    try {
      await api.updateUser({
        id: user.id,
        is_active: !user.is_active,
      });
      mutate();
      toast.success(`用户${user.is_active ? "已禁用" : "已启用"}`);
    } catch (error) {
      toast.error("操作失败");
    }
  };

  const confirmDelete = async () => {
    if (!deleteTarget || deleteTarget.type !== "user") return;

    try {
      await api.deleteUser(deleteTarget.id);
      mutate();
      toast.success("用户删除成功");
      setDeleteConfirmOpen(false);
      setDeleteTarget(null);
    } catch (error) {
      toast.error("删除失败");
    }
  };

  if (error) {
    return (
      <Card>
        <CardContent className="p-4 sm:p-6">
          <div className="text-center text-destructive">
            加载失败：{error.message}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl">用户管理</h1>
          <p className="text-muted-foreground mt-1">管理系统用户和权限</p>
        </div>
        <Button onClick={handleCreateUser} className="w-full sm:w-auto">
          <Plus className="h-4 w-4 mr-2" />
          创建用户
        </Button>
      </div>

      {/* Search and filters */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg">用户列表</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex flex-col gap-4 lg:flex-row lg:items-center">
              <div className="relative flex-1 max-w-sm">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索用户名或真实姓名..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-10"
                />
              </div>

              <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
                <Select value={selectedRole} onValueChange={handleRoleFilter}>
                  <SelectTrigger className="w-full sm:w-[140px]">
                    <SelectValue placeholder="所有角色" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有角色</SelectItem>
                    {rolesData?.map((role) => (
                      <SelectItem key={role.id} value={role.name}>
                        {role.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={selectedStatus}
                  onValueChange={handleStatusFilter}
                >
                  <SelectTrigger className="w-full sm:w-[120px]">
                    <SelectValue placeholder="所有状态" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">所有状态</SelectItem>
                    <SelectItem value="active">启用</SelectItem>
                    <SelectItem value="inactive">禁用</SelectItem>
                    <SelectItem value="superuser">超级管理员</SelectItem>
                    <SelectItem value="regular">普通用户</SelectItem>
                  </SelectContent>
                </Select>

                {hasActiveFilters && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearFilters}
                    className="w-full sm:w-auto"
                  >
                    <X className="h-4 w-4 mr-1" />
                    清除筛选
                  </Button>
                )}
              </div>
            </div>

            {/* Filter indicators */}
            {hasActiveFilters && (
              <div className="flex flex-wrap gap-2">
                {searchTerm && (
                  <Badge variant="secondary" className="text-xs">
                    搜索: {searchTerm}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                      onClick={() => handleSearch("")}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {selectedRole && selectedRole !== "all" && (
                  <Badge variant="secondary" className="text-xs">
                    角色: {selectedRole}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                      onClick={() => handleRoleFilter("all")}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {selectedStatus && selectedStatus !== "all" && (
                  <Badge variant="secondary" className="text-xs">
                    状态:{" "}
                    {selectedStatus === "active"
                      ? "启用"
                      : selectedStatus === "inactive"
                        ? "禁用"
                        : selectedStatus === "superuser"
                          ? "超级管理员"
                          : "普通用户"}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                      onClick={() => handleStatusFilter("all")}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
              </div>
            )}
          </div>

          {/* Users table */}
          {isLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <Skeleton key={i} className="h-16 w-full" />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {/* Desktop table */}
              <div className="hidden md:block">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>用户名</TableHead>
                      <TableHead>真实姓名</TableHead>
                      <TableHead>角色</TableHead>
                      <TableHead>状态</TableHead>
                      <TableHead>超级管理员</TableHead>
                      <TableHead className="text-right">操作</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredUsers.map((user) => (
                      <TableRow key={user.id}>
                        <TableCell className="font-medium">
                          {user.username}
                        </TableCell>
                        <TableCell>{user.real_name || "-"}</TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {user.roles.map((role) => (
                              <Badge key={role.id} variant="secondary">
                                {role.name}
                              </Badge>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Switch
                              checked={user.is_active}
                              onCheckedChange={() => handleToggleActive(user)}
                            />
                            <span className="text-sm">
                              {user.is_active ? "启用" : "禁用"}
                            </span>
                          </div>
                        </TableCell>
                        <TableCell>
                          {user.is_superuser ? (
                            <Badge variant="destructive">
                              <Shield className="h-3 w-3 mr-1" />
                              超级管理员
                            </Badge>
                          ) : (
                            <Badge variant="outline">普通用户</Badge>
                          )}
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleAssignRoles(user)}
                            >
                              <Shield className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditUser(user)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteUser(user)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile cards */}
              <div className="md:hidden space-y-4">
                {filteredUsers.map((user) => (
                  <Card key={user.id}>
                    <CardContent className="p-4">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-medium">{user.username}</h3>
                            {user.real_name && (
                              <p className="text-sm text-muted-foreground">
                                {user.real_name}
                              </p>
                            )}
                          </div>
                          <div className="flex items-center gap-2">
                            <Switch
                              checked={user.is_active}
                              onCheckedChange={() => handleToggleActive(user)}
                            />
                            <span className="text-sm">
                              {user.is_active ? "启用" : "禁用"}
                            </span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex flex-wrap gap-1">
                            {user.roles.map((role) => (
                              <Badge key={role.id} variant="secondary">
                                {role.name}
                              </Badge>
                            ))}
                          </div>

                          {user.is_superuser && (
                            <Badge variant="destructive" className="w-fit">
                              <Shield className="h-3 w-3 mr-1" />
                              超级管理员
                            </Badge>
                          )}
                        </div>

                        <div className="flex items-center gap-2 pt-2 border-t">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleAssignRoles(user)}
                            className="flex-1"
                          >
                            <Shield className="h-4 w-4 mr-1" />
                            角色
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditUser(user)}
                            className="flex-1"
                          >
                            <Edit className="h-4 w-4 mr-1" />
                            编辑
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteUser(user)}
                            className="flex-1"
                          >
                            <Trash2 className="h-4 w-4 mr-1" />
                            删除
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {filteredUsers.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  {hasActiveFilters ? "没有找到匹配的用户" : "暂无用户"}
                </div>
              )}
            </div>
          )}
        </CardContent>

        {/* Pagination */}
        {usersData && usersData.total > 0 && (
          <TablePagination
            currentPage={pagination.page}
            totalPages={calculateTotalPages(usersData.total, pagination.size)}
            totalItems={usersData.total}
            pageSize={pagination.size}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        )}
      </Card>

      {/* Dialogs */}
      <UserDialog
        open={userDialogOpen}
        onOpenChange={setUserDialogOpen}
        user={selectedUser}
        onSuccess={() => mutate()}
      />

      <RoleAssignmentDialog
        open={roleAssignmentOpen}
        onOpenChange={setRoleAssignmentOpen}
        userId={selectedUserId}
        onSuccess={() => mutate()}
      />

      <DeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        title="删除用户"
        description="确定要删除此用户吗？此操作无法撤销。"
        onConfirm={confirmDelete}
      />
    </div>
  );
}
