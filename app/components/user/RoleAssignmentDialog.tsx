import { useEffect, useState } from "react";
import { toast } from "sonner";
import useS<PERSON> from "swr";
import { api } from "../../lib/api";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Card, CardContent } from "../ui/card";
import { Checkbox } from "../ui/checkbox";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Skeleton } from "../ui/skeleton";

interface RoleAssignmentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  userId: number | null;
  onSuccess: () => void;
}

export function RoleAssignmentDialog({
  open,
  onOpenChange,
  userId,
  onSuccess,
}: RoleAssignmentDialogProps) {
  const [selectedRoleIds, setSelectedRoleIds] = useState<number[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: roles, isLoading: rolesLoading } = useSWR(
    "roles",
    api.getRoles,
    { revalidateOnFocus: false }
  );

  const { data: usersData } = useSWR(
    userId ? ["users"] : null,
    () => api.getUsers({ page: 1, size: 100 }),
    { revalidateOnFocus: false }
  );

  const currentUser = usersData?.items.find((u) => u.id === userId);

  useEffect(() => {
    if (currentUser) {
      setSelectedRoleIds(currentUser.roles.map((role) => role.id));
    } else {
      setSelectedRoleIds([]);
    }
  }, [currentUser]);

  const handleRoleToggle = (roleId: number) => {
    setSelectedRoleIds((prev) =>
      prev.includes(roleId)
        ? prev.filter((id) => id !== roleId)
        : [...prev, roleId]
    );
  };

  const handleSubmit = async () => {
    if (!userId) return;

    setIsSubmitting(true);
    try {
      await api.assignRoles({
        user_id: userId,
        role_ids: selectedRoleIds,
      });

      toast.success("角色分配成功");
      onSuccess();
      onOpenChange(false);
    } catch (error: any) {
      toast.error(error.message || "角色分配失败");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!userId) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl">
        <DialogHeader>
          <DialogTitle>角色分配 - {currentUser?.username}</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {rolesLoading ? (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <Skeleton key={i} className="h-20 w-full" />
              ))}
            </div>
          ) : (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {roles?.map((role) => {
                const isSelected = selectedRoleIds.includes(role.id);
                const permissionCount = role.permissions
                  ? role.permissions.split(",").length
                  : 0;

                return (
                  <Card
                    key={role.id}
                    className={`cursor-pointer transition-colors ${
                      isSelected ? "border-primary bg-primary/5" : ""
                    }`}
                    onClick={() => handleRoleToggle(role.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <Checkbox
                          checked={isSelected}
                          onCheckedChange={() => handleRoleToggle(role.id)}
                          onClick={(e) => e.stopPropagation()}
                        />
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-medium">{role.name}</h4>
                            <Badge variant="secondary" className="text-xs">
                              {permissionCount} 个权限
                            </Badge>
                          </div>
                          <p className="text-sm text-muted-foreground">
                            {role.description}
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          )}

          <div className="flex justify-between items-center pt-4 border-t">
            <div className="text-sm text-muted-foreground">
              已选择 {selectedRoleIds.length} 个角色
            </div>
            <div className="flex gap-3">
              <Button variant="outline" onClick={() => onOpenChange(false)}>
                取消
              </Button>
              <Button onClick={handleSubmit} disabled={isSubmitting}>
                {isSubmitting ? "保存中..." : "保存"}
              </Button>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
