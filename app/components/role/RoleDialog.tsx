import { Search, X } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import useSWR from "swr";
import {
  api,
  compressPermissions,
  expandWildcardPermissions,
} from "../../lib/api";
import type {
  CreateRoleRequest,
  Role,
  UpdateRoleRequest,
} from "../../types/auth";
import { PermissionSelector } from "../permission/PermissionSelector";
import { Button } from "../ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { ScrollArea } from "../ui/scroll-area";

interface RoleDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  role?: Role | null;
  onSuccess: () => void;
}

interface FormData {
  name: string;
  description: string;
}

export function RoleDialog({
  open,
  onOpenChange,
  role,
  onSuccess,
}: RoleDialogProps) {
  const isEdit = !!role;
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");

  // Use role ID as key to force re-render when editing different roles
  const dialogKey = role ? `edit-${role.id}` : "create";

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    getValues,
    formState: { errors, isSubmitting },
  } = useForm<FormData>({
    defaultValues: {
      name: role?.name || "",
      description: role?.description || "",
    },
  });

  // Fetch available permissions
  const { data: permissions, isLoading: permissionsLoading } = useSWR(
    "permissions",
    api.getPermissions,
    { revalidateOnFocus: false }
  );

  // Reset form when dialog opens/closes or role changes
  useEffect(() => {
    if (open && role) {
      // When editing, set the values
      reset({
        name: role.name,
        description: role.description,
      });
    } else if (open && !role) {
      // When creating, clear the form
      reset({
        name: "",
        description: "",
      });
      setSelectedPermissions([]);
      setSearchQuery("");
    } else if (!open) {
      // Clear search when dialog closes
      setSearchQuery("");
    }
  }, [open, role, reset]);

  // Handle permissions separately
  useEffect(() => {
    if (open && role && permissions) {
      // Use existing permission strings and expand wildcards
      const existingPermissionStrings = role.permissions
        ? role.permissions
            .split(",")
            .map((p) => p.trim())
            .filter(Boolean)
        : [];

      // Expand wildcard permissions to actual permissions for the selector
      const expandedPermissions = expandWildcardPermissions(
        existingPermissionStrings,
        permissions
      );
      setSelectedPermissions(expandedPermissions);
    } else if (open && !role) {
      // Only clear permissions if we're not editing a role
      setSelectedPermissions([]);
    }
  }, [open, role, permissions]);

  const onSubmit = async (data: FormData) => {
    try {
      // Compress permissions to use wildcards where possible
      const compressedPermissions = permissions
        ? compressPermissions(selectedPermissions, permissions)
        : selectedPermissions;

      if (isEdit) {
        const updateData: UpdateRoleRequest = {
          id: role!.id,
          name: data.name,
          description: data.description,
          permissions: compressedPermissions,
        };
        await api.updateRole(updateData);
        toast.success("角色更新成功");
      } else {
        const createData: CreateRoleRequest = {
          name: data.name,
          description: data.description,
          permissions: compressedPermissions,
        };
        await api.createRole(createData);
        toast.success("角色创建成功");
      }

      onSuccess();
      onOpenChange(false);
    } catch (error: any) {
      toast.error(error.message || "操作失败");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>{isEdit ? "编辑角色" : "创建角色"}</DialogTitle>
        </DialogHeader>

        <form
          key={dialogKey}
          onSubmit={handleSubmit(onSubmit)}
          className="space-y-6"
        >
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">角色名称 *</Label>
              <Input
                id="name"
                {...register("name", {
                  required: "角色名称不能为空",
                  minLength: { value: 2, message: "角色名称至少2个字符" },
                })}
                placeholder="输入角色名称"
              />
              {errors.name && (
                <p className="text-sm text-destructive">
                  {errors.name.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">角色描述 *</Label>
              <Input
                id="description"
                {...register("description", {
                  required: "角色描述不能为空",
                })}
                placeholder="输入角色描述"
              />
              {errors.description && (
                <p className="text-sm text-destructive">
                  {errors.description.message}
                </p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label>权限配置</Label>

            {/* Search input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="搜索权限..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 pr-9"
              />
              {searchQuery && (
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setSearchQuery("")}
                  className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                >
                  <X className="h-3 w-3" />
                </Button>
              )}
            </div>

            <ScrollArea className="h-[400px] border rounded-lg p-4">
              {permissions && (
                <PermissionSelector
                  permissions={permissions}
                  selectedPermissions={selectedPermissions}
                  onSelectionChange={setSelectedPermissions}
                  searchQuery={searchQuery}
                />
              )}
            </ScrollArea>
          </div>

          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
            >
              取消
            </Button>
            <Button type="submit" disabled={isSubmitting || permissionsLoading}>
              {isSubmitting ? "保存中..." : isEdit ? "更新" : "创建"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
