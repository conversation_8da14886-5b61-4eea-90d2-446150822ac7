import { Shield, Users } from "lucide-react";
import { useState } from "react";
import { AppLayout } from "./layout/AppLayout";
import { RoleManagement } from "./role/RoleManagement";
import { Toaster } from "./ui/sonner";
import { UserManagement } from "./user/UserManagement";

type ActiveTab = "users" | "roles";

export function AuthApp() {
  const [activeTab, setActiveTab] = useState<ActiveTab>("users");

  const renderContent = () => {
    switch (activeTab) {
      case "users":
        return <UserManagement />;
      case "roles":
        return <RoleManagement />;
      default:
        return <UserManagement />;
    }
  };

  return (
    <>
      <AppLayout>
        <div className="space-y-6">
          {/* Navigation tabs */}
          <div className="border-b border-border">
            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                onClick={() => setActiveTab("users")}
                className={`flex items-center gap-2 whitespace-nowrap py-2 px-1 border-b-2 transition-colors ${
                  activeTab === "users"
                    ? "border-primary text-primary"
                    : "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
                }`}
              >
                <Users className="h-4 w-4" />
                用户管理
              </button>
              <button
                onClick={() => setActiveTab("roles")}
                className={`flex items-center gap-2 whitespace-nowrap py-2 px-1 border-b-2 transition-colors ${
                  activeTab === "roles"
                    ? "border-primary text-primary"
                    : "border-transparent text-muted-foreground hover:text-foreground hover:border-border"
                }`}
              >
                <Shield className="h-4 w-4" />
                角色管理
              </button>
            </nav>
          </div>

          {/* Content */}
          <div>{renderContent()}</div>
        </div>
      </AppLayout>

      <Toaster position="top-right" />
    </>
  );
}
