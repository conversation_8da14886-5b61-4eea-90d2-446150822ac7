import { ChevronDown, ChevronRight } from "lucide-react";
import React, { useMemo, useState } from "react";
import type { Permission } from "../../types/auth";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Checkbox } from "../ui/checkbox";

interface PermissionSelectorProps {
  permissions: Permission[];
  selectedPermissions: string[];
  onSelectionChange: (permissions: string[]) => void;
  searchQuery?: string;
}

interface GroupedPermissions {
  [service: string]: {
    [module: string]: Permission[];
  };
}

export function PermissionSelector({
  permissions,
  selectedPermissions,
  onSelectionChange,
  searchQuery = "",
}: PermissionSelectorProps) {
  const [expandedServices, setExpandedServices] = useState<Set<string>>(
    new Set()
  );
  const [expandedModules, setExpandedModules] = useState<Set<string>>(
    new Set()
  );

  // Filter permissions based on search query
  const filteredPermissions = useMemo(() => {
    if (!searchQuery.trim()) {
      return permissions;
    }

    const query = searchQuery.toLowerCase();
    return permissions.filter((permission) => {
      return (
        permission.name?.toLowerCase().includes(query) ||
        permission.description?.toLowerCase().includes(query) ||
        permission.service.toLowerCase().includes(query) ||
        permission.module.toLowerCase().includes(query) ||
        permission.action.toLowerCase().includes(query) ||
        permission.type.toLowerCase().includes(query)
      );
    });
  }, [permissions, searchQuery]);

  // Group filtered permissions by service and module
  const groupedPermissions = useMemo(() => {
    const grouped: GroupedPermissions = {};

    filteredPermissions.forEach((permission) => {
      if (!grouped[permission.service]) {
        grouped[permission.service] = {};
      }
      if (!grouped[permission.service][permission.module]) {
        grouped[permission.service][permission.module] = [];
      }
      grouped[permission.service][permission.module].push(permission);
    });

    return grouped;
  }, [filteredPermissions]);

  const toggleService = (service: string) => {
    const newExpanded = new Set(expandedServices);
    if (newExpanded.has(service)) {
      newExpanded.delete(service);
    } else {
      newExpanded.add(service);
    }
    setExpandedServices(newExpanded);
  };

  const toggleModule = (service: string, module: string) => {
    const key = `${service}.${module}`;
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(key)) {
      newExpanded.delete(key);
    } else {
      newExpanded.add(key);
    }
    setExpandedModules(newExpanded);
  };

  const getPermissionString = (permission: Permission) => {
    return `${permission.type}:${permission.service}:${permission.module}:${permission.action}`;
  };

  const isPermissionSelected = (permission: Permission) => {
    return selectedPermissions.includes(getPermissionString(permission));
  };

  const togglePermission = (permission: Permission) => {
    const permString = getPermissionString(permission);
    const newSelected = selectedPermissions.includes(permString)
      ? selectedPermissions.filter((p) => p !== permString)
      : [...selectedPermissions, permString];

    onSelectionChange(newSelected);
  };

  const toggleAllInModule = (service: string, module: string) => {
    const modulePermissions = groupedPermissions[service][module];
    const modulePermStrings = modulePermissions.map(getPermissionString);

    const allSelected = modulePermStrings.every((p) =>
      selectedPermissions.includes(p)
    );

    if (allSelected) {
      // Deselect all in module
      const newSelected = selectedPermissions.filter(
        (p) => !modulePermStrings.includes(p)
      );
      onSelectionChange(newSelected);
    } else {
      // Select all in module
      const newSelected = [
        ...new Set([...selectedPermissions, ...modulePermStrings]),
      ];
      onSelectionChange(newSelected);
    }
  };

  const toggleAllInService = (service: string) => {
    const servicePermissions: Permission[] = [];
    Object.values(groupedPermissions[service]).forEach((modulePerms) => {
      servicePermissions.push(...modulePerms);
    });

    const servicePermStrings = servicePermissions.map(getPermissionString);
    const allSelected = servicePermStrings.every((p) =>
      selectedPermissions.includes(p)
    );

    if (allSelected) {
      // Deselect all in service
      const newSelected = selectedPermissions.filter(
        (p) => !servicePermStrings.includes(p)
      );
      onSelectionChange(newSelected);
    } else {
      // Select all in service
      const newSelected = [
        ...new Set([...selectedPermissions, ...servicePermStrings]),
      ];
      onSelectionChange(newSelected);
    }
  };

  const getModuleSelectedCount = (service: string, module: string) => {
    const modulePermissions = groupedPermissions[service][module];
    return modulePermissions.filter(isPermissionSelected).length;
  };

  const getServiceSelectedCount = (service: string) => {
    let count = 0;
    Object.values(groupedPermissions[service]).forEach((modulePerms) => {
      count += modulePerms.filter(isPermissionSelected).length;
    });
    return count;
  };

  const getServiceTotalCount = (service: string) => {
    let count = 0;
    Object.values(groupedPermissions[service]).forEach((modulePerms) => {
      count += modulePerms.length;
    });
    return count;
  };

  // Auto-expand services and modules when searching
  React.useEffect(() => {
    if (searchQuery.trim() && Object.keys(groupedPermissions).length > 0) {
      // Expand all services and modules when searching
      const allServices = Object.keys(groupedPermissions);
      const allModules = Object.entries(groupedPermissions).flatMap(
        ([service, modules]) =>
          Object.keys(modules).map((module) => `${service}.${module}`)
      );

      setExpandedServices(new Set(allServices));
      setExpandedModules(new Set(allModules));
    }
  }, [searchQuery, groupedPermissions]);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3>权限配置</h3>
        <div className="flex items-center gap-2">
          {searchQuery && (
            <Badge variant="outline">
              找到 {filteredPermissions.length} 个权限
            </Badge>
          )}
          <Badge variant="secondary">
            已选择 {selectedPermissions.length} 个权限
          </Badge>
        </div>
      </div>

      {Object.keys(groupedPermissions).length === 0 && searchQuery ? (
        <div className="text-center text-muted-foreground py-8">
          <p>没有找到匹配 "{searchQuery}" 的权限</p>
        </div>
      ) : (
        <div className="space-y-2">
          {Object.entries(groupedPermissions).map(([service, modules]) => {
            const serviceSelectedCount = getServiceSelectedCount(service);
            const serviceTotalCount = getServiceTotalCount(service);
            const isServiceExpanded = expandedServices.has(service);

            return (
              <Card key={service} className="border">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Checkbox
                        checked={
                          serviceSelectedCount === serviceTotalCount &&
                          serviceTotalCount > 0
                        }
                        onCheckedChange={() => toggleAllInService(service)}
                        indeterminate={
                          serviceSelectedCount > 0 &&
                          serviceSelectedCount < serviceTotalCount
                        }
                      />
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleService(service)}
                        className="p-0 h-auto"
                      >
                        {isServiceExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </Button>
                      <CardTitle className="text-base">{service}</CardTitle>
                    </div>
                    <Badge variant="outline">
                      {serviceSelectedCount}/{serviceTotalCount}
                    </Badge>
                  </div>
                </CardHeader>

                {isServiceExpanded && (
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      {Object.entries(modules).map(([module, permissions]) => {
                        const moduleSelectedCount = getModuleSelectedCount(
                          service,
                          module
                        );
                        const moduleTotalCount = permissions.length;
                        const moduleKey = `${service}.${module}`;
                        const isModuleExpanded = expandedModules.has(moduleKey);

                        return (
                          <div key={module} className="border rounded-lg p-3">
                            <div className="flex items-center justify-between mb-2">
                              <div className="flex items-center gap-2">
                                <Checkbox
                                  checked={
                                    moduleSelectedCount === moduleTotalCount &&
                                    moduleTotalCount > 0
                                  }
                                  onCheckedChange={() =>
                                    toggleAllInModule(service, module)
                                  }
                                  indeterminate={
                                    moduleSelectedCount > 0 &&
                                    moduleSelectedCount < moduleTotalCount
                                  }
                                />
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => toggleModule(service, module)}
                                  className="p-0 h-auto"
                                >
                                  {isModuleExpanded ? (
                                    <ChevronDown className="h-4 w-4" />
                                  ) : (
                                    <ChevronRight className="h-4 w-4" />
                                  )}
                                </Button>
                                <span className="font-medium">{module}</span>
                              </div>
                              <Badge variant="outline" className="text-xs">
                                {moduleSelectedCount}/{moduleTotalCount}
                              </Badge>
                            </div>

                            {isModuleExpanded && (
                              <div className="ml-6 space-y-2">
                                {permissions.map((permission) => {
                                  const isSelected =
                                    isPermissionSelected(permission);

                                  return (
                                    <div
                                      key={getPermissionString(permission)}
                                      className="flex items-center gap-3 p-2 rounded border"
                                    >
                                      <Checkbox
                                        checked={isSelected}
                                        onCheckedChange={() =>
                                          togglePermission(permission)
                                        }
                                      />
                                      <div className="flex-1 min-w-0">
                                        <div className="flex items-center gap-2">
                                          <span className="font-medium">
                                            {permission.name ||
                                              permission.action}
                                          </span>
                                          <Badge
                                            variant="secondary"
                                            className="text-xs"
                                          >
                                            {permission.type}
                                          </Badge>
                                          <Badge
                                            variant="outline"
                                            className="text-xs"
                                          >
                                            {permission.action}
                                          </Badge>
                                        </div>
                                        {permission.description && (
                                          <p className="text-sm text-muted-foreground mt-1">
                                            {permission.description}
                                          </p>
                                        )}
                                      </div>
                                    </div>
                                  );
                                })}
                              </div>
                            )}
                          </div>
                        );
                      })}
                    </div>
                  </CardContent>
                )}
              </Card>
            );
          })}
        </div>
      )}
    </div>
  );
}
